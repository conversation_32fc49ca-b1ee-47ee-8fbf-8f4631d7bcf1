package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合同中台服务状态枚举
 *
 * <AUTHOR>
 * @date 2025/05/28
 */
@Getter
@AllArgsConstructor
public enum ContractCenterStatusEnum {

    WRITING(1, "填写中"),
    REJECTED(2, "复审失败"),
    PENDING_REVIEW(100, "内部复审"),
    PENDING_SIGNATURE(100, "用户签约"),
    SIGNED(4, "签署成功"),
    ;

    private final int code;
    private final String desc;

    public static ContractCenterStatusEnum getByCodeAndDesc(Integer code, String desc) {
        for (ContractCenterStatusEnum value : values()) {
            //100以下的是根据数值来，100以上根据文案来
            if (code < 100) {
                if (value.getCode() == code) {
                    return value;
                }
            }else{
                if (value.getDesc().equals(desc)) {
                    return value;
                }
            }
        }
        return null;
    }
}