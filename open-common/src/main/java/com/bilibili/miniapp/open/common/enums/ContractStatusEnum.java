package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合同状态枚举
 *
 * <AUTHOR>
 * @date 2025/05/28
 */
@Getter
@AllArgsConstructor
public enum ContractStatusEnum {

    AUDIT_FAILED(-1, "审核未通过"),
    PENDING_AUDIT(0, "审核中"),
    WAITING_SIGNATURE(1, "待签署"),
    INACTIVE(2, "未生效"),
    EFFECTIVE(3, "生效中"),
    EXPIRED(4, "已失效");

    private final int code;
    private final String desc;


    public static ContractStatusEnum getByContractCenterStatus(Integer contractCenterStatus, String desc) {

        if (contractCenterStatus == null) {
            return null;
        }

        ContractCenterStatusEnum status = ContractCenterStatusEnum.getByCodeAndDesc(contractCenterStatus, desc);
        if (status == null) {
            return null;
        }
        switch (status) {
            case REJECTED:
                return AUDIT_FAILED;
            case PENDING_SIGNATURE:
                return WAITING_SIGNATURE;
            case SIGNED:
                return INACTIVE;
            default:
                return null;
        }
    }


    /**
     * 根据状态码获取枚举
     */
    public static ContractStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ContractStatusEnum status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查状态码是否有效
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}