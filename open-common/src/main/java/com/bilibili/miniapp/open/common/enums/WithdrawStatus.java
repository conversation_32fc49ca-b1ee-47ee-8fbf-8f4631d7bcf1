package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/4
 */
@AllArgsConstructor
@Getter
public enum WithdrawStatus {

    FROZEN(0, "提现冻结"),
    READY_TO_WITHDRAW(1, "可提现"),
    WITHDRAWING(2, "提现中"),
    SUCCESS(3, "提现成功"),
    ;

    private final int code;
    private final String desc;

    public static WithdrawStatus getBySettlementStatus(SettlementStatus settlementStatus) {
        if (settlementStatus == null) {
            return null;
        }

        switch (settlementStatus) {
            case CANCELED:
                return READY_TO_WITHDRAW;
            case PAYMENT_FAILED:
            case AUDIT_REJECTED:
            case PENDING_CONFIRMATION:
            case PENDING_UPLOAD_INVOICE:
            case PENDING_AUDIT:
            case PENDING_PAYMENT:
                return WITHDRAWING;
            case PAYMENT_SUCCESS:
                return SUCCESS;
            default:
                return null;
        }
    }
}
