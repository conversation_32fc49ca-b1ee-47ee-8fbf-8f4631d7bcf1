package com.bilibili.miniapp.open.common.util;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class DateToQuarterConverter {

    public static String convertToQuarter(String logdate) {
        // 解析日期（格式：yyyyMMdd）
        LocalDate date = LocalDate.parse(logdate, DateTimeFormatter.BASIC_ISO_DATE);
        int year = date.getYear();
        int month = date.getMonthValue();

        // 计算季度（1-3月为1季度，4-6月为2季度，7-9月为3季度，10-12月为4季度）
        int quarter = (month + 2) / 3;

        return String.format("%d_%d", year, quarter);
    }
}
