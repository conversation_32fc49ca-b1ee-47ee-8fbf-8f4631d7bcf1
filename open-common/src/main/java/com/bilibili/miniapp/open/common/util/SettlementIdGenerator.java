package com.bilibili.miniapp.open.common.util;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 针对结算单定制的id生成器，不建议其他场景使用
 *
 * <AUTHOR>
 * @date 2025/6/11
 */
public class SettlementIdGenerator {
    // 自定义纪元时间：2025-06-01 00:00:00
    private static final long EPOCH = 1748707200;

    // 最大机器码（2位，范围0-99）
    private static final int MAX_MACHINE_ID = 99;

    // 最大序号（1位，范围0-9）
    private static final int MAX_SEQUENCE = 9;

    // 机器码
    private final int machineId;

    // 序号
    private final AtomicInteger sequence = new AtomicInteger(0);

    // 上次生成ID的时间戳
    private long lastTimestamp = -1L;

    private static final SettlementIdGenerator instance = new SettlementIdGenerator();

    public static long nextId(){
        return instance.getNextId();
    }

    private SettlementIdGenerator() {
        String localIp = IpUtil.getLocalIp();
        int lastSegment = Integer.parseInt(localIp.substring(localIp.lastIndexOf(".") + 1));
        this.machineId = 0xFF & lastSegment % 100;
    }

    /**
     * 生成12位唯一ID，格式：时间戳(9位)+机器码(2位)+序号(1位)
     * 在  2058年10月11日 23:59:59 后变成 13 位数字
     */
    private synchronized long getNextId() {
        long currentTimestamp = getCurrentSecond();

        if (currentTimestamp < lastTimestamp) {
            throw new RuntimeException("时钟回退，拒绝生成ID");
        }

        if (currentTimestamp == lastTimestamp) {
            int currentSequence = sequence.incrementAndGet();
            if (currentSequence > MAX_SEQUENCE) {
                currentTimestamp = waitNextSecond(lastTimestamp);
                sequence.set(0);
            }
        } else {
            sequence.set(0);
        }

        lastTimestamp = currentTimestamp;

        // 组合ID：时间戳(9位) + 机器码(2位) + 序号(1位)
        return ((currentTimestamp - EPOCH) * 1000) + (machineId * 10L) + sequence.get();
    }

    private long getCurrentSecond() {
        return System.currentTimeMillis() / 1000;
    }

    /**
     * 正常雪花算法是等待1ms，由于缩短的数值长度，这里等待时间变成了1s，会导致在高并发下性能稍差
     */
    private long waitNextSecond(long lastTimestamp) {
        long currentTimestamp = getCurrentSecond();
        while (currentTimestamp <= lastTimestamp) {
            currentTimestamp = getCurrentSecond();
        }
        return currentTimestamp;
    }
}