package com.bilibili.miniapp.open.portal.controller.open_api;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.service.biz.settlement.IaaSettlementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/31
 */
@Slf4j
@RestController
@RequestMapping("/open_api/v1/settlement/berserker/callback")
public class IaaSettlementCallbackController extends AbstractController {

    @Resource
    private IaaSettlementService iaaSettlementService;


    @GetMapping("/daily/settlement/complete")
    public Response<Void> dailySettlementComplete(
            @RequestParam(value = "log_date" ,required = false) String readyDate) {

        iaaSettlementService.onDailySettlementsReady(readyDate);
        return Response.SUCCESS();
    }


}
