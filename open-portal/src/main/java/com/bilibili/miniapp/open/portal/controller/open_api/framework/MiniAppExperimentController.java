package com.bilibili.miniapp.open.portal.controller.open_api.framework;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.annotations.MiniAppValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.ExperimentMapper;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.experiment.GeneralExperimentVo;
import com.bilibili.miniapp.open.service.experiment.IExperimentService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/6/10
 * @description 通用实验接口
 */
@Slf4j
@RestController
@RequestMapping("/open_api/v1/miniapp/experiment")
public class MiniAppExperimentController extends AbstractController {

    @Autowired
    private IExperimentService iExperimentService;
    /**
     * 通用实验返回
     */
    @GetMapping("/general")
    @Operation(description = "通用实验返回")
    @MiniAppValidation
    @MainSiteLoginValidation
    public Response<GeneralExperimentVo> generalExperiment(
            Context context
    ){
        if (Objects.isNull(context)||Objects.isNull(context.getMid())) {
            return Response.SUCCESS(GeneralExperimentVo.getDefault());
        }

        return Response.SUCCESS(ExperimentMapper.MAPPER.toVo(iExperimentService.getGeneralParam(context.getMid())));
    }

}
