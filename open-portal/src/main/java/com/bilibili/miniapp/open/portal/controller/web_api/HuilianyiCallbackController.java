package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.service.biz.settlement.ISettlementService;
import com.bilibili.miniapp.open.service.biz.settlement.IaaSettlementService;
import com.bilibili.miniapp.open.service.rpc.http.model.HlyCallbackBatchData;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiExpenseCallback;
import io.vavr.control.Try;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/28
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/settlement/hly/callback")
public class HuilianyiCallbackController extends AbstractController {

    @Resource
    private IaaSettlementService iaaSettlementService;

    @Autowired
    private ISettlementService settlementService;

    @PostMapping("/expense")
    public Response<Void> expenseCallback(
            @RequestBody HlyCallbackBatchData<HuilianyiExpenseCallback> expenseCallback) {


        expenseCallback.getDataList().forEach(callback -> {
            Try.run(() -> {
                iaaSettlementService.listenHuilianyiCallback(callback);
            }).onFailure(t -> {
                log.error("Fail to handle hly callback, msg={}", callback, t);
            });
        });

        settlementService.processCallBack(expenseCallback.getDataList());

        return Response.SUCCESS();
    }

}
