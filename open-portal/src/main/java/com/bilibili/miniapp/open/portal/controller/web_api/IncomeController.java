package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.IncomeControllerMapper;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.income.IncomeDetailVo;
import com.bilibili.miniapp.open.portal.vo.income.IncomeDetailQueryVo;
import com.bilibili.miniapp.open.portal.vo.income.IncomeSummaryVo;
import com.bilibili.miniapp.open.service.biz.income.IIncomeService;
import com.bilibili.miniapp.open.service.bo.income.IncomeDetailBo;
import com.bilibili.miniapp.open.service.bo.income.IncomeDetailQueryBo;
import com.bilibili.miniapp.open.service.bo.income.IncomeSummaryBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.stream.Collectors;

/**
 * 收入相关接口
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/platform/income")
public class IncomeController extends AbstractController {

    @Autowired
    private IIncomeService incomeService;

    /**
     * 获取收入汇总
     */
    @GetMapping("/summary")
    @MainSiteLoginValidation
    public Response<IncomeSummaryVo> getIncomeSummary(Context context) {

        IncomeSummaryBo incomeSummary = incomeService.getIncomeSummary(context.getMid());

        return Response.SUCCESS(IncomeControllerMapper.MAPPER.boToVo(incomeSummary));
    }

    /**
     * 获取收入明细
     */
    @GetMapping("/details")
    @MainSiteLoginValidation
    public Response<PageResult<IncomeDetailVo>> getIncomeDetails(Context context, IncomeDetailQueryVo request) {

        IncomeDetailQueryBo incomeDetailQueryBo = IncomeControllerMapper.MAPPER.voToBo(request);

        PageResult<IncomeDetailBo> pageResult = incomeService.queryIncomeDetails(context.getMid(), incomeDetailQueryBo);

        return Response.SUCCESS(
                PageResult.<IncomeDetailVo>builder()
                        .records(pageResult.getRecords().stream()
                                .map(IncomeControllerMapper.MAPPER::boToVo)
                                .collect(Collectors.toList()))
                        .total(pageResult.getTotal())
                        .build()
        );
    }
}
