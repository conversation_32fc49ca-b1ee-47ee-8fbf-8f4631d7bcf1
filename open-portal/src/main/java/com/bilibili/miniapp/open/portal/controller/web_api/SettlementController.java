package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.SettlementControllerMapper;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.settlement.*;
import com.bilibili.miniapp.open.service.biz.settlement.ISettlementService;
import com.bilibili.miniapp.open.service.bo.settlement.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 结算相关接口
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/platform/settlement")
public class SettlementController extends AbstractController {

    @Autowired
    private ISettlementService settlementService;

    /**
     * 获取结算日期列表
     */
    @GetMapping("/date")
    @MainSiteLoginValidation
    public Response<SettlementDateListVo> getSettlementDates(Context context,
                                                             @RequestParam("app_id") String appId) {

        SettlementDateListBo settlementDates = settlementService.getSettlementDates(context.getMid(), appId);

        return Response.SUCCESS(SettlementControllerMapper.MAPPER.boToVo(settlementDates));
    }

    /**
     * 获取结算预览信息
     */
    @GetMapping("/preview")
    @MainSiteLoginValidation
    public Response<SettlementPreviewVo> getSettlementPreview(Context context,
                                                              @RequestParam("app_id") String appId,
                                                              @RequestParam("accrual_ids") List<String> accrualIds) {

        SettlementPreviewBo respBo = settlementService.getSettlementPreview(context.getMid(), appId, accrualIds);

        return Response.SUCCESS(SettlementControllerMapper.MAPPER.boToVo(respBo));
    }

    /**
     * 获取结算列表
     */
    @GetMapping("/list")
    @MainSiteLoginValidation
    public Response<PageResult<SettlementItemVo>> getSettlementList(Context context,
                                                                    @RequestParam(value = "app_id") String appId,
                                                                    @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                    @RequestParam(value = "size", defaultValue = "20") Integer size,
                                                                    @RequestParam(value = "begin_time", required = false) Long beginTime,
                                                                    @RequestParam(value = "end_time", required = false) Long endTime,
                                                                    @RequestParam(value = "settlement_status", required = false) Integer settlementStatus) {

        PageResult<SettlementItemBo> pageResult = settlementService.getSettlementList(context.getMid(), appId, page, size, beginTime, endTime, settlementStatus);

        List<SettlementItemVo> settlementItemVos = pageResult.getRecords().stream()
                .map(SettlementControllerMapper.MAPPER::boToVo)
                .collect(Collectors.toList());

        PageResult<SettlementItemVo> voPageResult = new PageResult<>(pageResult.getTotal(), settlementItemVos);

        return Response.SUCCESS(voPageResult);
    }

    /**
     * 获取结算详情
     */
    @GetMapping("/detail")
    @MainSiteLoginValidation
    public Response<SettlementDetailVo> getSettlementDetail(Context context,
                                                            @RequestParam("settlement_id") String settlementId) {

        SettlementDetailBo settlementDetail = settlementService.getSettlementDetail(context.getMid(), settlementId);

        return Response.SUCCESS(SettlementControllerMapper.MAPPER.detailBoToVo(settlementDetail));
    }

    /**
     * 上传发票文件
     */
    @PostMapping("/invoice/upload")
    @MainSiteLoginValidation
    public Response<InvoiceUploadResultVo> uploadInvoice(Context context,
                                                         @RequestParam("settlement_id") String settlementId,
                                                         @RequestPart("file") MultipartFile file) {

        InvoiceUploadBo uploadResult = settlementService.uploadInvoice(context.getMid(), settlementId, file);

        return Response.SUCCESS(InvoiceUploadResultVo.builder()
                .oid(uploadResult.getOid())
                .url(uploadResult.getUrl())
                .build());
    }

    /**
     * 确认结算单
     */
    @PostMapping("/{settlement_id}/confirm")
    @MainSiteLoginValidation
    public Response<Void> confirmSettlement(Context context,
                                            @PathVariable("settlement_id") String settlementId) {

        settlementService.confirmSettlement(context.getMid(), settlementId);

        return Response.SUCCESS(null);
    }

    /**
     * 取消结算单
     */
    @PostMapping("/{settlement_id}/cancel")
    @MainSiteLoginValidation
    public Response<Void> cancelSettlement(Context context,
                                           @PathVariable("settlement_id") String settlementId) {

        settlementService.cancelSettlement(context.getMid(), settlementId);

        return Response.SUCCESS(null);
    }

    @GetMapping("/invoice/base")
    @MainSiteLoginValidation
    public Response<InvoiceIssuanceBaseInfoVo> getInvoiceBaseInfo(Context context) {

        InvoiceIssuanceBaseInfoBo invoiceBaseInfo = settlementService.getInvoiceBaseInfo();

        return Response.SUCCESS(SettlementControllerMapper.MAPPER.boToVo(invoiceBaseInfo));
    }

    /**
     * 创建结算单
     */
    @PostMapping("/order")
    @MainSiteLoginValidation
    public Response<Void> createSettlement(Context context,
                                           @RequestBody SettlementCreateVo request) {

        SettlementCreateBo reqBo = SettlementControllerMapper.MAPPER.voToBo(request);
        settlementService.createSettlement(context.getMid(), reqBo);

        return Response.SUCCESS(null);
    }

    /**
     * 发起提现单
     */
    @PostMapping("/order/initiate")
    @MainSiteLoginValidation
    public Response<Void> initiateSettlementOrder(Context context,
                                                  @RequestBody SettlementOrderInitiateVo request) {

        SettlementOrderInitiateBo reqBo = SettlementControllerMapper.MAPPER.voToBo(request);
        settlementService.initiateSettlementOrder(context.getMid(), reqBo);

        return Response.SUCCESS(null);
    }
}
