package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.common.util.TimeUtil;
import com.bilibili.miniapp.open.portal.vo.experiment.GeneralExperimentVo;
import com.bilibili.miniapp.open.service.bo.experiment.GeneralExperimentBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/6/10
 * @description 实验参数转换类
 */
@Mapper
public interface ExperimentMapper {
    ExperimentMapper MAPPER = Mappers.getMapper(ExperimentMapper.class);


    GeneralExperimentVo toVo(GeneralExperimentBo bo);
}
