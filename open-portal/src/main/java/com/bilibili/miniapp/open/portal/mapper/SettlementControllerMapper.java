package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.settlement.*;
import com.bilibili.miniapp.open.service.bo.settlement.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;

/**
 * 结算控制器映射器
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Mapper
public interface SettlementControllerMapper {

    SettlementControllerMapper MAPPER = Mappers.getMapper(SettlementControllerMapper.class);

    /**
     * 结算日期BO转VO
     */
    SettlementDateVo boToVo(SettlementDateBo bo);

    /**
     * 结算日期列表BO转VO
     */
    SettlementDateListVo boToVo(SettlementDateListBo bo);

    InvoiceIssuanceBaseInfoVo boToVo(InvoiceIssuanceBaseInfoBo bo);

    /**
     * 结算预览响应BO转VO
     * 将分转换为元，保留2位小数
     */
    @Mapping(target = "withdrawApplyAmount", expression = "java(com.bilibili.miniapp.open.common.util.SettlementUtil.convertCentToYuan(bo.getWithdrawApplyAmount()))")
    @Mapping(target = "taxFee", expression = "java(com.bilibili.miniapp.open.common.util.SettlementUtil.convertCentToYuan(bo.getTaxFee()))")
    @Mapping(target = "actualWithdrawAmount", expression = "java(com.bilibili.miniapp.open.common.util.SettlementUtil.convertCentToYuan(bo.getActualWithdrawAmount()))")
    SettlementPreviewVo boToVo(SettlementPreviewBo bo);

    /**
     * 结算项BO转VO
     * 将分转换为元，保留2位小数
     */
    @Mapping(target = "withdrawApplyAmount", expression = "java(com.bilibili.miniapp.open.common.util.SettlementUtil.convertCentToYuan(bo.getWithdrawApplyAmount()))")
    @Mapping(target = "taxFee", expression = "java(com.bilibili.miniapp.open.common.util.SettlementUtil.convertCentToYuan(bo.getTaxFee()))")
    @Mapping(target = "actualWithdrawAmount", expression = "java(com.bilibili.miniapp.open.common.util.SettlementUtil.convertCentToYuan(bo.getActualWithdrawAmount()))")
    SettlementItemVo boToVo(SettlementItemBo bo);

    /**
     * 结算详情BO转VO
     * 将分转换为元，保留2位小数，时间格式化为字符串
     */
    @Mapping(target = "actualWithdrawAmount", expression = "java(com.bilibili.miniapp.open.common.util.SettlementUtil.convertCentToYuan(bo.getActualWithdrawAmount()))")
    SettlementDetailVo detailBoToVo(SettlementDetailBo bo);

    /**
     * 创建结算单请求VO转BO
     */
    SettlementCreateBo voToBo(SettlementCreateVo vo);

    /**
     * 发起提现单请求VO转BO
     */
    SettlementOrderInitiateBo voToBo(SettlementOrderInitiateVo vo);

    default String formatTimestamp(Timestamp timestamp) {
        if (timestamp == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(timestamp);
    }
}
