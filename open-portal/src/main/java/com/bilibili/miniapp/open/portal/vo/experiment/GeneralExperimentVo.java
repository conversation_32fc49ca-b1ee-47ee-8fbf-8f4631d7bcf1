package com.bilibili.miniapp.open.portal.vo.experiment;

import com.bilibili.miniapp.open.service.bo.experiment.GeneralExperimentBo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10
 * @description TODO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonSerialize
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class GeneralExperimentVo implements Serializable {


    /**
     * 命中的实验tunnelId
     */
    private List<Long> hitTunnelIds;

    public static GeneralExperimentVo getDefault() {
        return GeneralExperimentVo.builder()
                .hitTunnelIds(Collections.emptyList())
                .build();
    }
}
