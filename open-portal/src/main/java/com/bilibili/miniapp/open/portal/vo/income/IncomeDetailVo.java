package com.bilibili.miniapp.open.portal.vo.income;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 收入明细项VO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class IncomeDetailItemVo {
    
    /**
     * 小程序名称
     */
    private String appName;
    
    /**
     * 小程序ID
     */
    private String appId;
    
    /**
     * 收入时间
     */
    private String incomeDate;
    
    /**
     * 流量类型
     */
    private Integer trafficType;
    
    /**
     * 收入金额（元）
     */
    private String incomeAmount;
    
    /**
     * 通道费（元）
     */
    private String channelFee;
    
    /**
     * 可分成收入金额（元）
     */
    private String distributableIncomeAmount;
    
    /**
     * 实际收入金额（元）
     */
    private String actualIncomeAmount;
    
    /**
     * 提现状态
     */
    private Integer withdrawStatus;
    
    /**
     * 通道费比例
     */
    private String channelFeeRatio;
    
    /**
     * 分成比例
     */
    private String distributableRatio;
}
