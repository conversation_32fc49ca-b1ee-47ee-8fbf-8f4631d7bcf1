package com.bilibili.miniapp.open.portal.vo.settlement;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 发起提现单请求VO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SettlementOrderInitiateVo {

    /**
     * 结算单ID
     */
    @NotBlank(message = "settlement_id不能为空")
    private String settlementId;

    /**
     * 发票OID
     */
    @NotBlank(message = "invoice_oid不能为空")
    private String invoiceOid;

    /**
     * 发票URL
     */
    @NotBlank(message = "invoice_url不能为空")
    private String invoiceUrl;
}
