package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppUserAccessLogPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 用户id
     */
    private Long mid;

    /**
     * 小程序id
     */
    private String appId;

    /**
     * 软删
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 0-小程序 1-装扮
     */
    private Integer type;

    private static final long serialVersionUID = 1L;
}