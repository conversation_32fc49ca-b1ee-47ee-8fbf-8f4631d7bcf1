package com.bilibili.miniapp.open.repository.mysql.settlement.mapper;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettleRulePo;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettleRulePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface IaaSettleRuleDao {
    long countByExample(IaaSettleRulePoExample example);

    int deleteByExample(IaaSettleRulePoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(IaaSettleRulePo record);

    int insertBatch(List<IaaSettleRulePo> records);

    int insertUpdateBatch(List<IaaSettleRulePo> records);

    int insert(IaaSettleRulePo record);

    int insertUpdateSelective(IaaSettleRulePo record);

    int insertSelective(IaaSettleRulePo record);

    List<IaaSettleRulePo> selectByExample(IaaSettleRulePoExample example);

    IaaSettleRulePo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IaaSettleRulePo record, @Param("example") IaaSettleRulePoExample example);

    int updateByExample(@Param("record") IaaSettleRulePo record, @Param("example") IaaSettleRulePoExample example);

    int updateByPrimaryKeySelective(IaaSettleRulePo record);

    int updateByPrimaryKey(IaaSettleRulePo record);
}