package com.bilibili.miniapp.open.repository.mysql.settlement.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IaaSettleRulePo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 周期，格式:yyyy_Q如 2025Q1为2025_1
     */
    private String period;

    /**
     * 商业流量提现比例， 万分比
     */
    private String bPct;

    /**
     * 商业流量crm充值比例， 万分比
     */
    private String bCrmPct;

    /**
     * 自然流量阈值以下提现比例， 万分比
     */
    private String nBelowPct;

    /**
     * 自然流量阈值以上提现比例， 万分比 ,注意和阈值以下部分是梯度比例。这个万分比只作用于超过阈值以上的部分。然后总计就是求和
     */
    private String nAbovePct;

    /**
     * 自然流量月流水阈值
     */
    private String nThreshold;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}