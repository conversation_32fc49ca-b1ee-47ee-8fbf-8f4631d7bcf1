package com.bilibili.miniapp.open.repository.mysql.settlement.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class IaaSettleRulePoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public IaaSettleRulePoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPeriodIsNull() {
            addCriterion("period is null");
            return (Criteria) this;
        }

        public Criteria andPeriodIsNotNull() {
            addCriterion("period is not null");
            return (Criteria) this;
        }

        public Criteria andPeriodEqualTo(String value) {
            addCriterion("period =", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodNotEqualTo(String value) {
            addCriterion("period <>", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodGreaterThan(String value) {
            addCriterion("period >", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodGreaterThanOrEqualTo(String value) {
            addCriterion("period >=", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodLessThan(String value) {
            addCriterion("period <", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodLessThanOrEqualTo(String value) {
            addCriterion("period <=", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodLike(String value) {
            addCriterion("period like", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodNotLike(String value) {
            addCriterion("period not like", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodIn(List<String> values) {
            addCriterion("period in", values, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodNotIn(List<String> values) {
            addCriterion("period not in", values, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodBetween(String value1, String value2) {
            addCriterion("period between", value1, value2, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodNotBetween(String value1, String value2) {
            addCriterion("period not between", value1, value2, "period");
            return (Criteria) this;
        }

        public Criteria andBPctIsNull() {
            addCriterion("b_pct is null");
            return (Criteria) this;
        }

        public Criteria andBPctIsNotNull() {
            addCriterion("b_pct is not null");
            return (Criteria) this;
        }

        public Criteria andBPctEqualTo(String value) {
            addCriterion("b_pct =", value, "bPct");
            return (Criteria) this;
        }

        public Criteria andBPctNotEqualTo(String value) {
            addCriterion("b_pct <>", value, "bPct");
            return (Criteria) this;
        }

        public Criteria andBPctGreaterThan(String value) {
            addCriterion("b_pct >", value, "bPct");
            return (Criteria) this;
        }

        public Criteria andBPctGreaterThanOrEqualTo(String value) {
            addCriterion("b_pct >=", value, "bPct");
            return (Criteria) this;
        }

        public Criteria andBPctLessThan(String value) {
            addCriterion("b_pct <", value, "bPct");
            return (Criteria) this;
        }

        public Criteria andBPctLessThanOrEqualTo(String value) {
            addCriterion("b_pct <=", value, "bPct");
            return (Criteria) this;
        }

        public Criteria andBPctLike(String value) {
            addCriterion("b_pct like", value, "bPct");
            return (Criteria) this;
        }

        public Criteria andBPctNotLike(String value) {
            addCriterion("b_pct not like", value, "bPct");
            return (Criteria) this;
        }

        public Criteria andBPctIn(List<String> values) {
            addCriterion("b_pct in", values, "bPct");
            return (Criteria) this;
        }

        public Criteria andBPctNotIn(List<String> values) {
            addCriterion("b_pct not in", values, "bPct");
            return (Criteria) this;
        }

        public Criteria andBPctBetween(String value1, String value2) {
            addCriterion("b_pct between", value1, value2, "bPct");
            return (Criteria) this;
        }

        public Criteria andBPctNotBetween(String value1, String value2) {
            addCriterion("b_pct not between", value1, value2, "bPct");
            return (Criteria) this;
        }

        public Criteria andBCrmPctIsNull() {
            addCriterion("b_crm_pct is null");
            return (Criteria) this;
        }

        public Criteria andBCrmPctIsNotNull() {
            addCriterion("b_crm_pct is not null");
            return (Criteria) this;
        }

        public Criteria andBCrmPctEqualTo(String value) {
            addCriterion("b_crm_pct =", value, "bCrmPct");
            return (Criteria) this;
        }

        public Criteria andBCrmPctNotEqualTo(String value) {
            addCriterion("b_crm_pct <>", value, "bCrmPct");
            return (Criteria) this;
        }

        public Criteria andBCrmPctGreaterThan(String value) {
            addCriterion("b_crm_pct >", value, "bCrmPct");
            return (Criteria) this;
        }

        public Criteria andBCrmPctGreaterThanOrEqualTo(String value) {
            addCriterion("b_crm_pct >=", value, "bCrmPct");
            return (Criteria) this;
        }

        public Criteria andBCrmPctLessThan(String value) {
            addCriterion("b_crm_pct <", value, "bCrmPct");
            return (Criteria) this;
        }

        public Criteria andBCrmPctLessThanOrEqualTo(String value) {
            addCriterion("b_crm_pct <=", value, "bCrmPct");
            return (Criteria) this;
        }

        public Criteria andBCrmPctLike(String value) {
            addCriterion("b_crm_pct like", value, "bCrmPct");
            return (Criteria) this;
        }

        public Criteria andBCrmPctNotLike(String value) {
            addCriterion("b_crm_pct not like", value, "bCrmPct");
            return (Criteria) this;
        }

        public Criteria andBCrmPctIn(List<String> values) {
            addCriterion("b_crm_pct in", values, "bCrmPct");
            return (Criteria) this;
        }

        public Criteria andBCrmPctNotIn(List<String> values) {
            addCriterion("b_crm_pct not in", values, "bCrmPct");
            return (Criteria) this;
        }

        public Criteria andBCrmPctBetween(String value1, String value2) {
            addCriterion("b_crm_pct between", value1, value2, "bCrmPct");
            return (Criteria) this;
        }

        public Criteria andBCrmPctNotBetween(String value1, String value2) {
            addCriterion("b_crm_pct not between", value1, value2, "bCrmPct");
            return (Criteria) this;
        }

        public Criteria andNBelowPctIsNull() {
            addCriterion("n_below_pct is null");
            return (Criteria) this;
        }

        public Criteria andNBelowPctIsNotNull() {
            addCriterion("n_below_pct is not null");
            return (Criteria) this;
        }

        public Criteria andNBelowPctEqualTo(String value) {
            addCriterion("n_below_pct =", value, "nBelowPct");
            return (Criteria) this;
        }

        public Criteria andNBelowPctNotEqualTo(String value) {
            addCriterion("n_below_pct <>", value, "nBelowPct");
            return (Criteria) this;
        }

        public Criteria andNBelowPctGreaterThan(String value) {
            addCriterion("n_below_pct >", value, "nBelowPct");
            return (Criteria) this;
        }

        public Criteria andNBelowPctGreaterThanOrEqualTo(String value) {
            addCriterion("n_below_pct >=", value, "nBelowPct");
            return (Criteria) this;
        }

        public Criteria andNBelowPctLessThan(String value) {
            addCriterion("n_below_pct <", value, "nBelowPct");
            return (Criteria) this;
        }

        public Criteria andNBelowPctLessThanOrEqualTo(String value) {
            addCriterion("n_below_pct <=", value, "nBelowPct");
            return (Criteria) this;
        }

        public Criteria andNBelowPctLike(String value) {
            addCriterion("n_below_pct like", value, "nBelowPct");
            return (Criteria) this;
        }

        public Criteria andNBelowPctNotLike(String value) {
            addCriterion("n_below_pct not like", value, "nBelowPct");
            return (Criteria) this;
        }

        public Criteria andNBelowPctIn(List<String> values) {
            addCriterion("n_below_pct in", values, "nBelowPct");
            return (Criteria) this;
        }

        public Criteria andNBelowPctNotIn(List<String> values) {
            addCriterion("n_below_pct not in", values, "nBelowPct");
            return (Criteria) this;
        }

        public Criteria andNBelowPctBetween(String value1, String value2) {
            addCriterion("n_below_pct between", value1, value2, "nBelowPct");
            return (Criteria) this;
        }

        public Criteria andNBelowPctNotBetween(String value1, String value2) {
            addCriterion("n_below_pct not between", value1, value2, "nBelowPct");
            return (Criteria) this;
        }

        public Criteria andNAbovePctIsNull() {
            addCriterion("n_above_pct is null");
            return (Criteria) this;
        }

        public Criteria andNAbovePctIsNotNull() {
            addCriterion("n_above_pct is not null");
            return (Criteria) this;
        }

        public Criteria andNAbovePctEqualTo(String value) {
            addCriterion("n_above_pct =", value, "nAbovePct");
            return (Criteria) this;
        }

        public Criteria andNAbovePctNotEqualTo(String value) {
            addCriterion("n_above_pct <>", value, "nAbovePct");
            return (Criteria) this;
        }

        public Criteria andNAbovePctGreaterThan(String value) {
            addCriterion("n_above_pct >", value, "nAbovePct");
            return (Criteria) this;
        }

        public Criteria andNAbovePctGreaterThanOrEqualTo(String value) {
            addCriterion("n_above_pct >=", value, "nAbovePct");
            return (Criteria) this;
        }

        public Criteria andNAbovePctLessThan(String value) {
            addCriterion("n_above_pct <", value, "nAbovePct");
            return (Criteria) this;
        }

        public Criteria andNAbovePctLessThanOrEqualTo(String value) {
            addCriterion("n_above_pct <=", value, "nAbovePct");
            return (Criteria) this;
        }

        public Criteria andNAbovePctLike(String value) {
            addCriterion("n_above_pct like", value, "nAbovePct");
            return (Criteria) this;
        }

        public Criteria andNAbovePctNotLike(String value) {
            addCriterion("n_above_pct not like", value, "nAbovePct");
            return (Criteria) this;
        }

        public Criteria andNAbovePctIn(List<String> values) {
            addCriterion("n_above_pct in", values, "nAbovePct");
            return (Criteria) this;
        }

        public Criteria andNAbovePctNotIn(List<String> values) {
            addCriterion("n_above_pct not in", values, "nAbovePct");
            return (Criteria) this;
        }

        public Criteria andNAbovePctBetween(String value1, String value2) {
            addCriterion("n_above_pct between", value1, value2, "nAbovePct");
            return (Criteria) this;
        }

        public Criteria andNAbovePctNotBetween(String value1, String value2) {
            addCriterion("n_above_pct not between", value1, value2, "nAbovePct");
            return (Criteria) this;
        }

        public Criteria andNThresholdIsNull() {
            addCriterion("n_threshold is null");
            return (Criteria) this;
        }

        public Criteria andNThresholdIsNotNull() {
            addCriterion("n_threshold is not null");
            return (Criteria) this;
        }

        public Criteria andNThresholdEqualTo(String value) {
            addCriterion("n_threshold =", value, "nThreshold");
            return (Criteria) this;
        }

        public Criteria andNThresholdNotEqualTo(String value) {
            addCriterion("n_threshold <>", value, "nThreshold");
            return (Criteria) this;
        }

        public Criteria andNThresholdGreaterThan(String value) {
            addCriterion("n_threshold >", value, "nThreshold");
            return (Criteria) this;
        }

        public Criteria andNThresholdGreaterThanOrEqualTo(String value) {
            addCriterion("n_threshold >=", value, "nThreshold");
            return (Criteria) this;
        }

        public Criteria andNThresholdLessThan(String value) {
            addCriterion("n_threshold <", value, "nThreshold");
            return (Criteria) this;
        }

        public Criteria andNThresholdLessThanOrEqualTo(String value) {
            addCriterion("n_threshold <=", value, "nThreshold");
            return (Criteria) this;
        }

        public Criteria andNThresholdLike(String value) {
            addCriterion("n_threshold like", value, "nThreshold");
            return (Criteria) this;
        }

        public Criteria andNThresholdNotLike(String value) {
            addCriterion("n_threshold not like", value, "nThreshold");
            return (Criteria) this;
        }

        public Criteria andNThresholdIn(List<String> values) {
            addCriterion("n_threshold in", values, "nThreshold");
            return (Criteria) this;
        }

        public Criteria andNThresholdNotIn(List<String> values) {
            addCriterion("n_threshold not in", values, "nThreshold");
            return (Criteria) this;
        }

        public Criteria andNThresholdBetween(String value1, String value2) {
            addCriterion("n_threshold between", value1, value2, "nThreshold");
            return (Criteria) this;
        }

        public Criteria andNThresholdNotBetween(String value1, String value2) {
            addCriterion("n_threshold not between", value1, value2, "nThreshold");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}