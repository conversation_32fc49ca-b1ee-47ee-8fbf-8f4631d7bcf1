<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.settlement.mapper.IaaSettleRuleDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettleRulePo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="period" jdbcType="VARCHAR" property="period" />
    <result column="b_pct" jdbcType="VARCHAR" property="bPct" />
    <result column="b_crm_pct" jdbcType="VARCHAR" property="bCrmPct" />
    <result column="n_below_pct" jdbcType="VARCHAR" property="nBelowPct" />
    <result column="n_above_pct" jdbcType="VARCHAR" property="nAbovePct" />
    <result column="n_threshold" jdbcType="VARCHAR" property="nThreshold" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, period, b_pct, b_crm_pct, n_below_pct, n_above_pct, n_threshold, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettleRulePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iaa_settle_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iaa_settle_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iaa_settle_rule
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettleRulePoExample">
    delete from iaa_settle_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettleRulePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into iaa_settle_rule (period, b_pct, b_crm_pct, 
      n_below_pct, n_above_pct, n_threshold, 
      ctime, mtime)
    values (#{period,jdbcType=VARCHAR}, #{bPct,jdbcType=VARCHAR}, #{bCrmPct,jdbcType=VARCHAR}, 
      #{nBelowPct,jdbcType=VARCHAR}, #{nAbovePct,jdbcType=VARCHAR}, #{nThreshold,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettleRulePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into iaa_settle_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="period != null">
        period,
      </if>
      <if test="bPct != null">
        b_pct,
      </if>
      <if test="bCrmPct != null">
        b_crm_pct,
      </if>
      <if test="nBelowPct != null">
        n_below_pct,
      </if>
      <if test="nAbovePct != null">
        n_above_pct,
      </if>
      <if test="nThreshold != null">
        n_threshold,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="period != null">
        #{period,jdbcType=VARCHAR},
      </if>
      <if test="bPct != null">
        #{bPct,jdbcType=VARCHAR},
      </if>
      <if test="bCrmPct != null">
        #{bCrmPct,jdbcType=VARCHAR},
      </if>
      <if test="nBelowPct != null">
        #{nBelowPct,jdbcType=VARCHAR},
      </if>
      <if test="nAbovePct != null">
        #{nAbovePct,jdbcType=VARCHAR},
      </if>
      <if test="nThreshold != null">
        #{nThreshold,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettleRulePoExample" resultType="java.lang.Long">
    select count(*) from iaa_settle_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iaa_settle_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.period != null">
        period = #{record.period,jdbcType=VARCHAR},
      </if>
      <if test="record.bPct != null">
        b_pct = #{record.bPct,jdbcType=VARCHAR},
      </if>
      <if test="record.bCrmPct != null">
        b_crm_pct = #{record.bCrmPct,jdbcType=VARCHAR},
      </if>
      <if test="record.nBelowPct != null">
        n_below_pct = #{record.nBelowPct,jdbcType=VARCHAR},
      </if>
      <if test="record.nAbovePct != null">
        n_above_pct = #{record.nAbovePct,jdbcType=VARCHAR},
      </if>
      <if test="record.nThreshold != null">
        n_threshold = #{record.nThreshold,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iaa_settle_rule
    set id = #{record.id,jdbcType=BIGINT},
      period = #{record.period,jdbcType=VARCHAR},
      b_pct = #{record.bPct,jdbcType=VARCHAR},
      b_crm_pct = #{record.bCrmPct,jdbcType=VARCHAR},
      n_below_pct = #{record.nBelowPct,jdbcType=VARCHAR},
      n_above_pct = #{record.nAbovePct,jdbcType=VARCHAR},
      n_threshold = #{record.nThreshold,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettleRulePo">
    update iaa_settle_rule
    <set>
      <if test="period != null">
        period = #{period,jdbcType=VARCHAR},
      </if>
      <if test="bPct != null">
        b_pct = #{bPct,jdbcType=VARCHAR},
      </if>
      <if test="bCrmPct != null">
        b_crm_pct = #{bCrmPct,jdbcType=VARCHAR},
      </if>
      <if test="nBelowPct != null">
        n_below_pct = #{nBelowPct,jdbcType=VARCHAR},
      </if>
      <if test="nAbovePct != null">
        n_above_pct = #{nAbovePct,jdbcType=VARCHAR},
      </if>
      <if test="nThreshold != null">
        n_threshold = #{nThreshold,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettleRulePo">
    update iaa_settle_rule
    set period = #{period,jdbcType=VARCHAR},
      b_pct = #{bPct,jdbcType=VARCHAR},
      b_crm_pct = #{bCrmPct,jdbcType=VARCHAR},
      n_below_pct = #{nBelowPct,jdbcType=VARCHAR},
      n_above_pct = #{nAbovePct,jdbcType=VARCHAR},
      n_threshold = #{nThreshold,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettleRulePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into iaa_settle_rule (period, b_pct, b_crm_pct, 
      n_below_pct, n_above_pct, n_threshold, 
      ctime, mtime)
    values (#{period,jdbcType=VARCHAR}, #{bPct,jdbcType=VARCHAR}, #{bCrmPct,jdbcType=VARCHAR}, 
      #{nBelowPct,jdbcType=VARCHAR}, #{nAbovePct,jdbcType=VARCHAR}, #{nThreshold,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      period = values(period),
      b_pct = values(b_pct),
      b_crm_pct = values(b_crm_pct),
      n_below_pct = values(n_below_pct),
      n_above_pct = values(n_above_pct),
      n_threshold = values(n_threshold),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      iaa_settle_rule
      (period,b_pct,b_crm_pct,n_below_pct,n_above_pct,n_threshold,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.period,jdbcType=VARCHAR},
        #{item.bPct,jdbcType=VARCHAR},
        #{item.bCrmPct,jdbcType=VARCHAR},
        #{item.nBelowPct,jdbcType=VARCHAR},
        #{item.nAbovePct,jdbcType=VARCHAR},
        #{item.nThreshold,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      iaa_settle_rule
      (period,b_pct,b_crm_pct,n_below_pct,n_above_pct,n_threshold,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.period,jdbcType=VARCHAR},
        #{item.bPct,jdbcType=VARCHAR},
        #{item.bCrmPct,jdbcType=VARCHAR},
        #{item.nBelowPct,jdbcType=VARCHAR},
        #{item.nAbovePct,jdbcType=VARCHAR},
        #{item.nThreshold,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      period = values(period),
      b_pct = values(b_pct),
      b_crm_pct = values(b_crm_pct),
      n_below_pct = values(n_below_pct),
      n_above_pct = values(n_above_pct),
      n_threshold = values(n_threshold),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettleRulePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into iaa_settle_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="period != null">
        period,
      </if>
      <if test="bPct != null">
        b_pct,
      </if>
      <if test="bCrmPct != null">
        b_crm_pct,
      </if>
      <if test="nBelowPct != null">
        n_below_pct,
      </if>
      <if test="nAbovePct != null">
        n_above_pct,
      </if>
      <if test="nThreshold != null">
        n_threshold,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="period != null">
        #{period,jdbcType=VARCHAR},
      </if>
      <if test="bPct != null">
        #{bPct,jdbcType=VARCHAR},
      </if>
      <if test="bCrmPct != null">
        #{bCrmPct,jdbcType=VARCHAR},
      </if>
      <if test="nBelowPct != null">
        #{nBelowPct,jdbcType=VARCHAR},
      </if>
      <if test="nAbovePct != null">
        #{nAbovePct,jdbcType=VARCHAR},
      </if>
      <if test="nThreshold != null">
        #{nThreshold,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="period != null">
        period = values(period),
      </if>
      <if test="bPct != null">
        b_pct = values(b_pct),
      </if>
      <if test="bCrmPct != null">
        b_crm_pct = values(b_crm_pct),
      </if>
      <if test="nBelowPct != null">
        n_below_pct = values(n_below_pct),
      </if>
      <if test="nAbovePct != null">
        n_above_pct = values(n_above_pct),
      </if>
      <if test="nThreshold != null">
        n_threshold = values(n_threshold),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>