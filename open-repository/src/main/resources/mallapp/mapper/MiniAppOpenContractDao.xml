<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenContractDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="signatory_name" jdbcType="VARCHAR" property="signatoryName" />
    <result column="signatory_phone" jdbcType="VARCHAR" property="signatoryPhone" />
    <result column="signatory_email" jdbcType="VARCHAR" property="signatoryEmail" />
    <result column="contact_address" jdbcType="VARCHAR" property="contactAddress" />
    <result column="contract_start_time" jdbcType="TIMESTAMP" property="contractStartTime" />
    <result column="contract_end_time" jdbcType="TIMESTAMP" property="contractEndTime" />
    <result column="contract_id" jdbcType="VARCHAR" property="contractId" />
    <result column="contract_status" jdbcType="TINYINT" property="contractStatus" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="contract_mtime" jdbcType="TIMESTAMP" property="contractMtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_id, signatory_name, signatory_phone, signatory_email, contact_address, contract_start_time, 
    contract_end_time, contract_id, contract_status, is_deleted, ctime, mtime, contract_mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_contract
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_open_contract
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_contract
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPoExample">
    delete from mini_app_open_contract
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_contract (app_id, signatory_name, signatory_phone, 
      signatory_email, contact_address, contract_start_time, 
      contract_end_time, contract_id, contract_status, 
      is_deleted, ctime, mtime, 
      contract_mtime)
    values (#{appId,jdbcType=VARCHAR}, #{signatoryName,jdbcType=VARCHAR}, #{signatoryPhone,jdbcType=VARCHAR}, 
      #{signatoryEmail,jdbcType=VARCHAR}, #{contactAddress,jdbcType=VARCHAR}, #{contractStartTime,jdbcType=TIMESTAMP}, 
      #{contractEndTime,jdbcType=TIMESTAMP}, #{contractId,jdbcType=VARCHAR}, #{contractStatus,jdbcType=TINYINT}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{contractMtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_contract
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="signatoryName != null">
        signatory_name,
      </if>
      <if test="signatoryPhone != null">
        signatory_phone,
      </if>
      <if test="signatoryEmail != null">
        signatory_email,
      </if>
      <if test="contactAddress != null">
        contact_address,
      </if>
      <if test="contractStartTime != null">
        contract_start_time,
      </if>
      <if test="contractEndTime != null">
        contract_end_time,
      </if>
      <if test="contractId != null">
        contract_id,
      </if>
      <if test="contractStatus != null">
        contract_status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="contractMtime != null">
        contract_mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="signatoryName != null">
        #{signatoryName,jdbcType=VARCHAR},
      </if>
      <if test="signatoryPhone != null">
        #{signatoryPhone,jdbcType=VARCHAR},
      </if>
      <if test="signatoryEmail != null">
        #{signatoryEmail,jdbcType=VARCHAR},
      </if>
      <if test="contactAddress != null">
        #{contactAddress,jdbcType=VARCHAR},
      </if>
      <if test="contractStartTime != null">
        #{contractStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractEndTime != null">
        #{contractEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractId != null">
        #{contractId,jdbcType=VARCHAR},
      </if>
      <if test="contractStatus != null">
        #{contractStatus,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractMtime != null">
        #{contractMtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_contract
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_contract
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.signatoryName != null">
        signatory_name = #{record.signatoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.signatoryPhone != null">
        signatory_phone = #{record.signatoryPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.signatoryEmail != null">
        signatory_email = #{record.signatoryEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.contactAddress != null">
        contact_address = #{record.contactAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.contractStartTime != null">
        contract_start_time = #{record.contractStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.contractEndTime != null">
        contract_end_time = #{record.contractEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.contractId != null">
        contract_id = #{record.contractId,jdbcType=VARCHAR},
      </if>
      <if test="record.contractStatus != null">
        contract_status = #{record.contractStatus,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.contractMtime != null">
        contract_mtime = #{record.contractMtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_contract
    set id = #{record.id,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      signatory_name = #{record.signatoryName,jdbcType=VARCHAR},
      signatory_phone = #{record.signatoryPhone,jdbcType=VARCHAR},
      signatory_email = #{record.signatoryEmail,jdbcType=VARCHAR},
      contact_address = #{record.contactAddress,jdbcType=VARCHAR},
      contract_start_time = #{record.contractStartTime,jdbcType=TIMESTAMP},
      contract_end_time = #{record.contractEndTime,jdbcType=TIMESTAMP},
      contract_id = #{record.contractId,jdbcType=VARCHAR},
      contract_status = #{record.contractStatus,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      contract_mtime = #{record.contractMtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPo">
    update mini_app_open_contract
    <set>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="signatoryName != null">
        signatory_name = #{signatoryName,jdbcType=VARCHAR},
      </if>
      <if test="signatoryPhone != null">
        signatory_phone = #{signatoryPhone,jdbcType=VARCHAR},
      </if>
      <if test="signatoryEmail != null">
        signatory_email = #{signatoryEmail,jdbcType=VARCHAR},
      </if>
      <if test="contactAddress != null">
        contact_address = #{contactAddress,jdbcType=VARCHAR},
      </if>
      <if test="contractStartTime != null">
        contract_start_time = #{contractStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractEndTime != null">
        contract_end_time = #{contractEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractId != null">
        contract_id = #{contractId,jdbcType=VARCHAR},
      </if>
      <if test="contractStatus != null">
        contract_status = #{contractStatus,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractMtime != null">
        contract_mtime = #{contractMtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPo">
    update mini_app_open_contract
    set app_id = #{appId,jdbcType=VARCHAR},
      signatory_name = #{signatoryName,jdbcType=VARCHAR},
      signatory_phone = #{signatoryPhone,jdbcType=VARCHAR},
      signatory_email = #{signatoryEmail,jdbcType=VARCHAR},
      contact_address = #{contactAddress,jdbcType=VARCHAR},
      contract_start_time = #{contractStartTime,jdbcType=TIMESTAMP},
      contract_end_time = #{contractEndTime,jdbcType=TIMESTAMP},
      contract_id = #{contractId,jdbcType=VARCHAR},
      contract_status = #{contractStatus,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      contract_mtime = #{contractMtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_contract (app_id, signatory_name, signatory_phone, 
      signatory_email, contact_address, contract_start_time, 
      contract_end_time, contract_id, contract_status, 
      is_deleted, ctime, mtime, 
      contract_mtime)
    values (#{appId,jdbcType=VARCHAR}, #{signatoryName,jdbcType=VARCHAR}, #{signatoryPhone,jdbcType=VARCHAR}, 
      #{signatoryEmail,jdbcType=VARCHAR}, #{contactAddress,jdbcType=VARCHAR}, #{contractStartTime,jdbcType=TIMESTAMP}, 
      #{contractEndTime,jdbcType=TIMESTAMP}, #{contractId,jdbcType=VARCHAR}, #{contractStatus,jdbcType=TINYINT}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{contractMtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      app_id = values(app_id),
      signatory_name = values(signatory_name),
      signatory_phone = values(signatory_phone),
      signatory_email = values(signatory_email),
      contact_address = values(contact_address),
      contract_start_time = values(contract_start_time),
      contract_end_time = values(contract_end_time),
      contract_id = values(contract_id),
      contract_status = values(contract_status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      contract_mtime = values(contract_mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_contract
      (app_id,signatory_name,signatory_phone,signatory_email,contact_address,contract_start_time,contract_end_time,contract_id,contract_status,is_deleted,ctime,mtime,contract_mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.appId,jdbcType=VARCHAR},
        #{item.signatoryName,jdbcType=VARCHAR},
        #{item.signatoryPhone,jdbcType=VARCHAR},
        #{item.signatoryEmail,jdbcType=VARCHAR},
        #{item.contactAddress,jdbcType=VARCHAR},
        #{item.contractStartTime,jdbcType=TIMESTAMP},
        #{item.contractEndTime,jdbcType=TIMESTAMP},
        #{item.contractId,jdbcType=VARCHAR},
        #{item.contractStatus,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.contractMtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_contract
      (app_id,signatory_name,signatory_phone,signatory_email,contact_address,contract_start_time,contract_end_time,contract_id,contract_status,is_deleted,ctime,mtime,contract_mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.appId,jdbcType=VARCHAR},
        #{item.signatoryName,jdbcType=VARCHAR},
        #{item.signatoryPhone,jdbcType=VARCHAR},
        #{item.signatoryEmail,jdbcType=VARCHAR},
        #{item.contactAddress,jdbcType=VARCHAR},
        #{item.contractStartTime,jdbcType=TIMESTAMP},
        #{item.contractEndTime,jdbcType=TIMESTAMP},
        #{item.contractId,jdbcType=VARCHAR},
        #{item.contractStatus,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.contractMtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      app_id = values(app_id),
      signatory_name = values(signatory_name),
      signatory_phone = values(signatory_phone),
      signatory_email = values(signatory_email),
      contact_address = values(contact_address),
      contract_start_time = values(contract_start_time),
      contract_end_time = values(contract_end_time),
      contract_id = values(contract_id),
      contract_status = values(contract_status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      contract_mtime = values(contract_mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_contract
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="signatoryName != null">
        signatory_name,
      </if>
      <if test="signatoryPhone != null">
        signatory_phone,
      </if>
      <if test="signatoryEmail != null">
        signatory_email,
      </if>
      <if test="contactAddress != null">
        contact_address,
      </if>
      <if test="contractStartTime != null">
        contract_start_time,
      </if>
      <if test="contractEndTime != null">
        contract_end_time,
      </if>
      <if test="contractId != null">
        contract_id,
      </if>
      <if test="contractStatus != null">
        contract_status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="contractMtime != null">
        contract_mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="signatoryName != null">
        #{signatoryName,jdbcType=VARCHAR},
      </if>
      <if test="signatoryPhone != null">
        #{signatoryPhone,jdbcType=VARCHAR},
      </if>
      <if test="signatoryEmail != null">
        #{signatoryEmail,jdbcType=VARCHAR},
      </if>
      <if test="contactAddress != null">
        #{contactAddress,jdbcType=VARCHAR},
      </if>
      <if test="contractStartTime != null">
        #{contractStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractEndTime != null">
        #{contractEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractId != null">
        #{contractId,jdbcType=VARCHAR},
      </if>
      <if test="contractStatus != null">
        #{contractStatus,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractMtime != null">
        #{contractMtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="appId != null">
        app_id = values(app_id),
      </if>
      <if test="signatoryName != null">
        signatory_name = values(signatory_name),
      </if>
      <if test="signatoryPhone != null">
        signatory_phone = values(signatory_phone),
      </if>
      <if test="signatoryEmail != null">
        signatory_email = values(signatory_email),
      </if>
      <if test="contactAddress != null">
        contact_address = values(contact_address),
      </if>
      <if test="contractStartTime != null">
        contract_start_time = values(contract_start_time),
      </if>
      <if test="contractEndTime != null">
        contract_end_time = values(contract_end_time),
      </if>
      <if test="contractId != null">
        contract_id = values(contract_id),
      </if>
      <if test="contractStatus != null">
        contract_status = values(contract_status),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="contractMtime != null">
        contract_mtime = values(contract_mtime),
      </if>
    </trim>
  </insert>
</mapper>