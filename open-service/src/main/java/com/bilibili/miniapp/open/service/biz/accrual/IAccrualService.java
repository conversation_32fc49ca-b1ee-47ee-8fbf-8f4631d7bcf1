package com.bilibili.miniapp.open.service.biz.accrual;

import com.bilibili.miniapp.open.common.enums.WithdrawStatus;
import com.bilibili.miniapp.open.service.bo.accrual.AccrualBo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/6
 */
public interface IAccrualService {

    List<AccrualBo> queryAccruals(String appId, WithdrawStatus withdrawStatus);

    List<AccrualBo> queryAccruals(String appId, List<String> accrualIds);

    void updateStatus(String appId, List<String> accrualIds, WithdrawStatus targetStatus);

    void createAccrual(String incomeDate);
}
