package com.bilibili.miniapp.open.service.biz.income.impl;

import com.alibaba.fastjson.JSON;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppDTO;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.TrafficType;
import com.bilibili.miniapp.open.common.enums.WithdrawStatus;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenIaaIncomeDetailDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPoExample;
import com.bilibili.miniapp.open.service.biz.account.IAccountService;
import com.bilibili.miniapp.open.service.biz.income.IIncomeService;
import com.bilibili.miniapp.open.service.biz.settlement.IaaSettleRuleService;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaSettleRuleBo;
import com.bilibili.miniapp.open.service.bo.income.*;
import com.bilibili.miniapp.open.service.mapper.IncomeBizMapper;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 收入服务实现
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Service
public class IncomeService implements IIncomeService {


    @Autowired
    private IAccountService accountService;

    @Autowired
    private MiniAppRemoteService miniAppRemoteService;

    @Autowired
    private MiniAppOpenIaaIncomeDetailDao iaaIncomeDetailDao;

    @Autowired
    private IaaSettleRuleService settleRuleService;

    private static final BigDecimal oneHundred = new BigDecimal(100);

    @Override
    public IncomeSummaryBo getIncomeSummary(Long mid) {

        AssertUtil.isTrue(accountService.isCompanyOwner(mid), ErrorCodeType.NO_PERMISSION);

        List<MiniAppDTO> miniApps = miniAppRemoteService.queryMainMiniAppsFromCache(mid);

        if (CollectionUtils.isEmpty(miniApps)) {
            return IncomeSummaryBo.builder()
                    .withdrawableAmount(0L)
                    .withdrawingAmount(0L)
                    .build();
        }

        List<String> appIds = miniApps.stream().map(MiniAppDTO::getAppId).collect(Collectors.toList());

        return calculateIncomeSummary(appIds);
    }

    private IncomeSummaryBo calculateIncomeSummary(List<String> appIds) {

        MiniAppOpenIaaIncomeDetailPoExample example = new MiniAppOpenIaaIncomeDetailPoExample();
        example.createCriteria()
                .andAppIdIn(appIds)
                .andWithdrawStatusIn(List.of(WithdrawStatus.READY_TO_WITHDRAW.getCode(), WithdrawStatus.WITHDRAWING.getCode()))
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenIaaIncomeDetailPo> incomeDetails = iaaIncomeDetailDao.selectByExample(example);

        if (CollectionUtils.isEmpty(incomeDetails)) {
            return IncomeSummaryBo.builder()
                    .withdrawableAmount(0L)
                    .withdrawingAmount(0L)
                    .build();
        }

        Map<Integer, List<MiniAppOpenIaaIncomeDetailPo>> statusMap = incomeDetails.stream()
                .collect(Collectors.groupingBy(MiniAppOpenIaaIncomeDetailPo::getWithdrawStatus));

        Long withdrawableAmount = calculateAmount(statusMap.get(WithdrawStatus.READY_TO_WITHDRAW.getCode()));
        Long withdrawingAmount = calculateAmount(statusMap.get(WithdrawStatus.WITHDRAWING.getCode()));

        return IncomeSummaryBo.builder()
                .withdrawableAmount(withdrawableAmount)
                .withdrawingAmount(withdrawingAmount)
                .build();
    }

    private Long calculateAmount(List<MiniAppOpenIaaIncomeDetailPo> incomeDetailPos) {
        if (CollectionUtils.isEmpty(incomeDetailPos)) {
            return 0L;
        }
        return incomeDetailPos.stream()
                .map(MiniAppOpenIaaIncomeDetailPo::getActualIncomeAmount)
                .filter(Objects::nonNull)
                .reduce(0L, Long::sum);
    }

    @Override
    public PageResult<IncomeDetailBo> queryIncomeDetails(Long mid, IncomeDetailQueryBo request) {

        validatePermission(mid, request.getAppId());

        List<MiniAppDTO> mainApps = miniAppRemoteService.queryMainMiniApps(mid);
        if (CollectionUtils.isEmpty(mainApps)) {
            return PageResult.emptyPageResult();
        }

        List<String> targetAppId = filterAppIdsByNameIfNecessary(mainApps, request.getAppName());

        MiniAppOpenIaaIncomeDetailPoExample example = buildQueryExample(targetAppId, request);

        long total = iaaIncomeDetailDao.countByExample(example);
        if (total == 0) {
            return PageResult.emptyPageResult();
        }

        Page page = Page.valueOf(request.getPage(), request.getSize());
        example.setLimit(page.getLimit());
        example.setOffset(page.getOffset());
        example.setOrderByClause("id desc");

        List<MiniAppOpenIaaIncomeDetailPo> incomeDetails = iaaIncomeDetailDao.selectByExample(example);

        Map<String, String> appNameMap = mainApps.stream()
                .collect(Collectors.toMap(MiniAppDTO::getAppId, MiniAppDTO::getName));

        List<IncomeDetailBo> records = incomeDetails.stream()
                .map(po -> IncomeBizMapper.MAPPER.toBo(po, appNameMap))
                .collect(Collectors.toList());

        return PageResult.<IncomeDetailBo>builder()
                .records(records)
                .total(Math.toIntExact(total))
                .build();
    }

    private List<String> filterAppIdsByNameIfNecessary(List<MiniAppDTO> mainApps, String fuzzyAppName) {
        List<String> result = new ArrayList<>();
        if (StringUtils.isBlank(fuzzyAppName)) {
            return result;
        }
        return mainApps.stream()
                .filter(app -> app.getName().contains(fuzzyAppName))
                .map(MiniAppDTO::getAppId)
                .collect(Collectors.toList());
    }

    private void validatePermission(long mid, String appId) {
        if (StringUtils.isNotBlank(appId)) {
            AssertUtil.isTrue(accountService.isCompanyOwner(mid), ErrorCodeType.NO_PERMISSION);
        }
        AssertUtil.isTrue(accountService.hasAnyPermission(mid, appId), ErrorCodeType.NO_PERMISSION);
    }

    private MiniAppOpenIaaIncomeDetailPoExample buildQueryExample(List<String> targetAppIds, IncomeDetailQueryBo request) {
        MiniAppOpenIaaIncomeDetailPoExample example = new MiniAppOpenIaaIncomeDetailPoExample();
        MiniAppOpenIaaIncomeDetailPoExample.Criteria criteria = example.createCriteria();

        criteria.andIsDeletedEqualTo(0);

        if (!CollectionUtils.isEmpty(targetAppIds)) {
            criteria.andAppIdIn(targetAppIds);
        }

        if (StringUtils.isNotBlank(request.getBeginTime())) {
            criteria.andLogDateGreaterThanOrEqualTo(request.getBeginTime());
        }
        if (StringUtils.isNotBlank(request.getEndTime())) {
            criteria.andLogDateLessThanOrEqualTo(request.getEndTime());
        }

        if (request.getTrafficType() != null) {
            criteria.andTrafficTypeEqualTo(request.getTrafficType());
        }

        if (request.getWithdrawStatus() != null) {
            criteria.andWithdrawStatusEqualTo(request.getWithdrawStatus());
        }

        return example;
    }

    @Override
    public void updateStatus(String appId, List<String> logDates, WithdrawStatus targetStatus) {
        MiniAppOpenIaaIncomeDetailPoExample example = new MiniAppOpenIaaIncomeDetailPoExample();
        MiniAppOpenIaaIncomeDetailPoExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId)
                .andLogDateIn(logDates)
                .andIsDeletedEqualTo(0);

        MiniAppOpenIaaIncomeDetailPo updatePo = new MiniAppOpenIaaIncomeDetailPo();
        updatePo.setWithdrawStatus(targetStatus.getCode());

        iaaIncomeDetailDao.updateByExampleSelective(updatePo, example);
    }

    @Override
    public List<IncomeDetailBo> queryIncomeDetails(String logDate) {
        List<MiniAppOpenIaaIncomeDetailPo> pos = queryPos(logDate);
        return pos.stream()
                .map(po -> IncomeBizMapper.MAPPER.toBo(po, Map.of()))
                .collect(Collectors.toList());
    }

    public void calculateAllAmount(IncomeDetailBo detail) {
        Long incomeAmount = detail.getIncomeAmount();
        detail.setChannelFee(calculateChannelFee());

        detail.setDistributableIncomeAmount(calculateDistributableIncomeAmount(incomeAmount, detail.getChannelFee()));

        IaaSettleRuleBo rule = settleRuleService.getIaaSettleRule(detail.getIncomeDate());
        String distributableRatio = getDistributableRatio(TrafficType.getByCode(detail.getTrafficType()), detail.getDistributableIncomeAmount(), rule);
        Long actualIncomeAmount = calculateActualIncomeAmount(detail.getDistributableIncomeAmount(), distributableRatio);
        detail.setActualIncomeAmount(actualIncomeAmount);

        detail.setDistributableRatio(distributableRatio);
        detail.setChannelFeeRatio("0%");
    }

    @Override
    public void updateAfterAccrualCompleted(List<IncomeDetailBo> incomeDetailBos) {
        incomeDetailBos.forEach(bo -> {
            MiniAppOpenIaaIncomeDetailPo updatePo = new MiniAppOpenIaaIncomeDetailPo();
            updatePo.setId(bo.getId());
            updatePo.setAccrualId(bo.getAccrualId());
            updatePo.setChannelFee(bo.getChannelFee());
            updatePo.setDistributableIncomeAmount(bo.getDistributableIncomeAmount());
            updatePo.setActualIncomeAmount(bo.getActualIncomeAmount());
            IncomeDetailExtra extra = IncomeDetailExtra.builder()
                    .channelFeeRatio(bo.getChannelFeeRatio())
                    .distributableRatio(bo.getDistributableRatio())
                    .build();
            updatePo.setExtra(JSON.toJSONString(extra));

            iaaIncomeDetailDao.updateByPrimaryKeySelective(updatePo);
        });
    }

    @Override
    public void unfreeze(String logDate) {
        MiniAppOpenIaaIncomeDetailPoExample example = new MiniAppOpenIaaIncomeDetailPoExample();
        MiniAppOpenIaaIncomeDetailPoExample.Criteria criteria = example.createCriteria();
        criteria.andLogDateLessThanOrEqualTo(logDate)
                .andWithdrawStatusEqualTo(WithdrawStatus.FROZEN.getCode())
                .andIsDeletedEqualTo(0);

        MiniAppOpenIaaIncomeDetailPo updatePo = new MiniAppOpenIaaIncomeDetailPo();
        updatePo.setWithdrawStatus(WithdrawStatus.READY_TO_WITHDRAW.getCode());

        iaaIncomeDetailDao.updateByExampleSelective(updatePo, example);
    }

    private List<MiniAppOpenIaaIncomeDetailPo> queryPos(String logDate) {
        int querySize = 1000;
        Long previousId = null;
        List<MiniAppOpenIaaIncomeDetailPo> pos = new ArrayList<>();
        while (true) {
            MiniAppOpenIaaIncomeDetailPoExample example = new MiniAppOpenIaaIncomeDetailPoExample();
            MiniAppOpenIaaIncomeDetailPoExample.Criteria criteria = example.createCriteria();
            criteria.andLogDateLessThanOrEqualTo(logDate)
                    .andAccrualIdEqualTo("")
                    .andIsDeletedEqualTo(0);
            if (previousId != null) {
                criteria.andIdGreaterThan(previousId);
            }
            example.setLimit(querySize);
            example.setOrderByClause("id");
            List<MiniAppOpenIaaIncomeDetailPo> thisPagePos = iaaIncomeDetailDao.selectByExample(example);

            if (CollectionUtils.isEmpty(thisPagePos)) {
                break;
            } else {
                pos.addAll(thisPagePos);
                previousId = thisPagePos.get(thisPagePos.size() - 1).getId();
            }
        }
        return pos;
    }

    /**
     * 目前固定为0
     */
    private Long calculateChannelFee() {
        return 0L;
    }

    private Long calculateDistributableIncomeAmount(Long incomeAmount, Long channelFee) {
        return incomeAmount - channelFee;
    }

    private Long calculateActualIncomeAmount(Long distributableIncomeAmount, String distributableRatio) {

        return new BigDecimal(distributableIncomeAmount)
                .multiply(new BigDecimal(distributableRatio))
                .divide(oneHundred, 0, RoundingMode.HALF_UP)
                .longValue();
    }

    private String getDistributableRatio(TrafficType trafficType, Long distributableIncomeAmount, IaaSettleRuleBo rule) {
        if (trafficType == TrafficType.NATURAL) {
            boolean overThreshold = distributableIncomeAmount > Long.parseLong(rule.getNThreshold());
            return overThreshold ? rule.getNAbovePct() : rule.getNBelowPct();
        }

        if (trafficType == TrafficType.BUSINESS) {
            return rule.getBPct();
        }
        throw new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "不存在的流量类型");
    }
}
