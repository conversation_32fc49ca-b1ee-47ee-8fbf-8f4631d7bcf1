package com.bilibili.miniapp.open.service.biz.settlement;

import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.service.bo.settlement.*;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiExpenseCallback;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 结算服务接口
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
public interface ISettlementService {

    /**
     * 获取结算日期列表
     *
     * @param mid   用户ID
     * @param appId 小程序ID
     * @return 结算日期列表
     */
    SettlementDateListBo getSettlementDates(Long mid, String appId);

    /**
     * 获取结算预览信息
     */
    SettlementPreviewBo getSettlementPreview(Long mid, String appId, List<String> accrualIds);

    /**
     * 上传发票文件
     *
     * @return 上传结果，包含oid和url
     */
    InvoiceUploadBo uploadInvoice(long mid, String settlementId, MultipartFile file);

    /**
     * 获取结算列表
     */
    PageResult<SettlementItemBo> getSettlementList(Long mid,
                                                   String appId,
                                                   Integer page,
                                                   Integer size,
                                                   Long beginTime,
                                                   Long endTime,
                                                   Integer settlementStatus);

    /**
     * 获取结算详情
     *
     * @param mid          用户ID
     * @param settlementId 结算单ID
     * @return 结算详情
     */
    SettlementDetailBo getSettlementDetail(Long mid, String settlementId);

    /**
     * 确认结算单
     *
     * @param mid          用户ID
     * @param settlementId 结算单ID
     */
    void confirmSettlement(Long mid, String settlementId);

    /**
     * 取消结算单
     *
     * @param mid          用户ID
     * @param settlementId 结算单ID
     */
    void cancelSettlement(Long mid, String settlementId);

    InvoiceIssuanceBaseInfoBo getInvoiceBaseInfo();

    /**
     * 创建结算单
     *
     * @param mid     用户ID
     * @param request 创建结算单请求
     */
    void createSettlement(Long mid, SettlementCreateBo request);

    /**
     * 发起提现单
     *
     * @param mid     用户ID
     * @param request 发起提现单请求
     */
    void initiateSettlementOrder(Long mid, SettlementOrderInitiateBo request);

    void processCallBack(List<HuilianyiExpenseCallback> callbacks);
}
