package com.bilibili.miniapp.open.service.biz.settlement.impl;

import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenAccrualSettlementMappingDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualSettlementMappingPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualSettlementMappingPoExample;
import com.bilibili.miniapp.open.service.biz.settlement.IAccrualSettleMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 预提单与结算单映射关系服务实现
 *
 * <AUTHOR>
 * @date 2025/6/11
 */
@Slf4j
@Service
public class AccrualSettleMappingServiceImpl implements IAccrualSettleMappingService {

    @Resource
    private MiniAppOpenAccrualSettlementMappingDao accrualSettlementMappingDao;

    @Override
    public void recordMapping(String settlementId, List<String> accrualIds) {
        if (settlementId == null || CollectionUtils.isEmpty(accrualIds)) {
            return;
        }

        List<MiniAppOpenAccrualSettlementMappingPo> mappingList = new ArrayList<>();
        Timestamp now = new Timestamp(System.currentTimeMillis());

        for (String accrualId : accrualIds) {
            MiniAppOpenAccrualSettlementMappingPo mapping = MiniAppOpenAccrualSettlementMappingPo.builder()
                    .settlementId(settlementId)
                    .accrualId(accrualId)
                    .ctime(now)
                    .mtime(now)
                    .isDeleted(0)
                    .build();
            mappingList.add(mapping);
        }
        accrualSettlementMappingDao.insertBatch(mappingList);
    }

    @Override
    public List<String> getAccrualIds(String settlementId) {
        if (settlementId == null) {
            return new ArrayList<>();
        }

        MiniAppOpenAccrualSettlementMappingPoExample example = new MiniAppOpenAccrualSettlementMappingPoExample();
        example.createCriteria()
                .andSettlementIdEqualTo(settlementId)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenAccrualSettlementMappingPo> mappingList = accrualSettlementMappingDao.selectByExample(example);

        if (CollectionUtils.isEmpty(mappingList)) {
            return new ArrayList<>();
        }

        return mappingList.stream()
                .map(MiniAppOpenAccrualSettlementMappingPo::getAccrualId)
                .collect(Collectors.toList());

    }
}
