package com.bilibili.miniapp.open.service.biz.settlement.impl;

import com.bilibili.miniapp.open.common.util.DateToQuarterConverter;
import com.bilibili.miniapp.open.repository.mysql.settlement.mapper.IaaSettleRuleDao;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettleRulePo;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettleRulePoExample;
import com.bilibili.miniapp.open.service.biz.settlement.IaaSettleRuleService;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaSettleRuleBo;
import com.bilibili.miniapp.open.service.mapper.SettlementMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/29
 **/

@Service
@Slf4j
public class IaaSettleRuleServiceImpl implements IaaSettleRuleService {

    @Resource
    private IaaSettleRuleDao iaaSettleRuleDao;
    @Override
    public IaaSettleRuleBo getIaaSettleRule(String logdate) {
        String period = DateToQuarterConverter.convertToQuarter(logdate);
        log.info("[IaaSettleRuleServiceImpl] getIaaSettleRule, logdate: {}, period: {}", logdate, period);
        IaaSettleRulePoExample example = new IaaSettleRulePoExample();
        example.createCriteria().andPeriodEqualTo(period);
        List<IaaSettleRulePo> iaaSettleRulePos = iaaSettleRuleDao.selectByExample(example);
        if (CollectionUtils.isEmpty(iaaSettleRulePos)) {
            log.warn("[IaaSettleRuleServiceImpl] getIaaSettleRule, no rule found for period: {}", period);
            return null;
        }
        return SettlementMapper.MAPPER.toBo(iaaSettleRulePos.get(0));
    }

    @Override
    public void saveIaaSettleRule(IaaSettleRuleBo iaaSettleRuleBo) {
        log.info("[IaaSettleRuleServiceImpl] saveIaaSettleRule, iaaSettleRuleBo: {}", iaaSettleRuleBo);
        IaaSettleRulePo po = SettlementMapper.MAPPER.toPo(iaaSettleRuleBo);
        IaaSettleRulePoExample example = new IaaSettleRulePoExample();
        example.createCriteria().andPeriodEqualTo(iaaSettleRuleBo.getPeriod());
        List<IaaSettleRulePo> existingRules = iaaSettleRuleDao.selectByExample(example);
        if (CollectionUtils.isEmpty(existingRules)) {
            iaaSettleRuleDao.insertSelective(po);
        } else {
            po.setId(existingRules.get(0).getId());
            iaaSettleRuleDao.updateByPrimaryKeySelective(po);
        }
    }
}
