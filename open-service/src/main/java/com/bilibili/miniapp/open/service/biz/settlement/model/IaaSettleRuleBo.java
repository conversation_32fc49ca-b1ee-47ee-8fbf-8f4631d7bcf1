package com.bilibili.miniapp.open.service.biz.settlement.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/29
 **/

@Data
public class IaaSettleRuleBo {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 周期，格式:yyyy_Q如 2025Q1为2025_1
     */
    private String period;

    /**
     * 商业流量提现比例， 万分比
     */
    private String bPct;

    /**
     * 商业流量crm充值比例， 万分比
     */
    private String bCrmPct;

    /**
     * 自然流量阈值以下提现比例， 万分比
     */
    private String nBelowPct;

    /**
     * 自然流量阈值以上提现比例， 万分比 ,注意和阈值以下部分是梯度比例。这个万分比只作用于超过阈值以上的部分。然后总计就是求和
     */
    private String nAbovePct;

    /**
     * 自然流量月流水阈值（元）
     */
    private String nThreshold;
}
