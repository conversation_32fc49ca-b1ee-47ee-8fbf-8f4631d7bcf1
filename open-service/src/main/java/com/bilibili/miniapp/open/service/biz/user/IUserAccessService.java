package com.bilibili.miniapp.open.service.biz.user;

import com.bilibili.miniapp.open.service.bo.user.UserAccessRecord;
import com.bilibili.miniapp.open.service.bo.user.UserRecentAccessDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/26
 **/
public interface IUserAccessService {

    /**
     * 记录用户访问小程序数据
     * @param mid 用户id
     * @param appId 小程序id
     */
    void recordUserAccess(Long mid, String appId);

    /**
     * 记录用户访问小程序数据(DB)
     * @param userAccessRecord
     */
    void recordUserAccessForDb(UserAccessRecord userAccessRecord);

    /**
     * 获取用户最近访问小程序的数据
     * @param mid
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<UserRecentAccessDetail> queryUserRecentAccess(Long mid, Integer pageNum, Integer pageSize);

    void recordUserAccessFb(Long mid, String appId);
}
