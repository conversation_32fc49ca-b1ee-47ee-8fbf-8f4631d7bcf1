package com.bilibili.miniapp.open.service.biz.user.impl;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppBaseInfoDto;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppUserAccessLogPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.IUserAccessRepository;
import com.bilibili.miniapp.open.service.biz.applet.IAppletUrlService;
import com.bilibili.miniapp.open.service.biz.user.IUserAccessCacheService;
import com.bilibili.miniapp.open.service.biz.user.IUserAccessQueryService;
import com.bilibili.miniapp.open.service.biz.user.IUserAccessService;
import com.bilibili.miniapp.open.service.bo.user.UserAccessRecord;
import com.bilibili.miniapp.open.service.bo.user.UserRecentAccessDetail;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.config.UserAccessConfig;
import com.bilibili.miniapp.open.service.databus.entity.UserAccessMsg;
import com.bilibili.miniapp.open.service.databus.producer.MiniAppUserAccessProducer;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/26
 **/

@Service
@Slf4j
public class UserAccessService implements IUserAccessService {

    @Resource
    private IUserAccessQueryService userAccessQueryService;

    @Resource
    private MiniAppUserAccessProducer userAccessProducer;

    @Resource
    private IUserAccessCacheService userAccessCacheService;

    @Resource
    private IUserAccessRepository userAccessRepository;

    @Resource
    private MiniAppRemoteService miniAppRemoteService;

    @Resource
    private ConfigCenter configCenter;

    @Resource
    private IAppletUrlService appletUrlService;

    @Override
    public void recordUserAccess(Long mid, String appId) {
        // 1. 判断小程序是否有效（缓存）
        validateApp(appId);
        // 2. 查询当前用户最近访问的小程序（缓存）
        List<UserAccessRecord> userAccessRecords = userAccessQueryService.getUserAccessRecord(mid);
        // 3. 如果当前用户访问过小程序则修改，否则新增（缓存）
        long now = System.currentTimeMillis();
        UserAccessRecord userAccessRecord = UserAccessRecord.builder()
                .mid(mid)
                .appId(appId)
                .mtime(new Timestamp(now))
                .build();

        // 4. 更新缓存 (这里可能出现4成功，5失败的情况) 目前没有加入重试机制可能会导致缓存不一致，所以先去掉
//        userAccessCacheService.cacheUserAccessRecord(userAccessRecord);
        // 5. 异步更新mysql
        userAccessProducer.publishUserAccessMsg(UserAccessMsg.builder()
                .appId(appId)
                .mid(mid)
                .ctime(now)
                .mtime(now)
                .build());
    }

    private void validateApp(String appId) {
        Map<String, MiniAppBaseInfoDto> baseInfoDtoMap = miniAppRemoteService.queryAppInfosWithinCache(Collections.singletonList(appId));
        Assert.isTrue(MapUtils.isNotEmpty(baseInfoDtoMap) && baseInfoDtoMap.containsKey(appId), "小程序不存在");
        Assert.isTrue(baseInfoDtoMap.get(appId).getOffline() == 0, "小程序已下架");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordUserAccessForDb(UserAccessRecord userAccessRecord) {
        Long mid = userAccessRecord.getMid();
        String appId = userAccessRecord.getAppId();
        // 查数据库
        List<MiniAppUserAccessLogPo> userAccessLogPos = userAccessRepository.queryByMid(mid);
        List<String> appIds = userAccessLogPos.stream().map(MiniAppUserAccessLogPo::getAppId).collect(Collectors.toList());
        if (appIds.contains(appId)) {
            userAccessRepository.updateAccessTime(mid, appId, userAccessRecord.getMtime());
        } else {
            userAccessRepository.save(userAccessRecord.toPo());
        }
        // 更新缓存
        List<UserAccessRecord> userAccessRecords = userAccessLogPos.stream().map(UserAccessRecord::fromPo).collect(Collectors.toList());
        userAccessRecords.add(userAccessRecord);
        userAccessCacheService.cacheUserAccessRecord(mid, userAccessRecords);
    }

    @Override
    public List<UserRecentAccessDetail> queryUserRecentAccess(Long mid, Integer pageNum, Integer pageSize) {
        if (pageSize > configCenter.getUserAccess().getMaxRecent()) {
            // 超出最大限制 直接返回空列表
            return Collections.emptyList();
        }
        List<UserAccessRecord> userAccessRecords = userAccessQueryService.getUserAccessRecord(mid, pageNum, pageSize);
        List<String> appIds = userAccessRecords.stream().map(UserAccessRecord::getAppId).collect(Collectors.toList());
        Map<String, MiniAppBaseInfoDto> appInfoDTOS = miniAppRemoteService.queryAppInfosWithinCache(appIds);
        List<UserRecentAccessDetail> userRecentAccesses = new ArrayList<>();
        for (UserAccessRecord userAccessRecord : userAccessRecords) {
            UserAccessConfig userAccess = configCenter.getUserAccess();
            if (StringUtils.equals(userAccess.getDecorateAppId(), userAccessRecord.getAppId())) {
                userRecentAccesses.add(UserRecentAccessDetail.builder()
                        .appId(userAccess.getDecorateAppId())
                        .type(3)
                        .icon(userAccess.getDecorateIcon())
                        .title(userAccess.getDecorateName())
                        .linkUrl(userAccess.getDecorateUrl())
                        .recentVisitTime(userAccessRecord.getMtime().getTime())
                        .summary(userAccess.getDecorateSummary())
                        .build());
                continue;
            }
            String appId = userAccessRecord.getAppId();
            MiniAppBaseInfoDto appInfoDTO = appInfoDTOS.get(appId);
            if (Objects.isNull(appInfoDTO)) {
                log.warn("[UserAccessService] 小程序[{}]不存在", appId);
                continue;
            }
            if (!Objects.equals(appInfoDTO.getOffline(), (byte) 0)) {
                log.warn("[UserAccessService] 小程序[{}]已下架", appId);
                continue;
            }
            userRecentAccesses.add(UserRecentAccessDetail.builder()
                    .appId(appId)
                    .type(3)
                    .icon(appInfoDTO.getLogo())
                    .title(appInfoDTO.getName())
                    .linkUrl(buildLinkUrl(appId, appInfoDTO.getAppletVersion()))
                    .recentVisitTime(userAccessRecord.getMtime().getTime())
                    .summary(appInfoDTO.getIntroduction())
                    .build());
        }
        return userRecentAccesses;
    }

    @Override
    public void recordUserAccessFb(Long mid, String appId) {
        UserAccessConfig userAccess = configCenter.getUserAccess();
        if (!StringUtils.equals(userAccess.getDecorateAppId(), appId)) {
            log.warn("[UserAccessService] 不属于装扮小程序{}", appId);
            return;
        }
        userAccessProducer.publishUserAccessMsg(UserAccessMsg.builder()
                .appId(appId)
                .mid(mid)
                .type(UserAccessRecord.UserAccessTypeEnum.DECORATE.getType())
                .ctime(System.currentTimeMillis())
                .mtime(System.currentTimeMillis())
                .build());
    }

    private String buildLinkUrl(String appId, Integer appletVersion) {
        return appletUrlService.getMainPageUrl(appId, appletVersion, Map.of("sourcefrom", 100401));
    }
}
