package com.bilibili.miniapp.open.service.biz.youku;

import com.bilibili.miniapp.open.common.enums.IsDeleted;
import com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppOpenYoukuOrderDao;
import com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppYoukuVideoDao;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenYoukuOrderPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenYoukuOrderPoExample;
import com.bilibili.miniapp.open.service.bo.youku.YkOrderCallbackReq;
import com.bilibili.miniapp.open.service.mapper.YkCallbackBizMapper;
import com.bilibili.miniapp.open.service.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/7
 **/

@Service
@Slf4j
public class YouKuCallbackService {

    @Resource
    private MiniAppOpenYoukuOrderDao youkuOrderDao;


    public void saveOrderCallback(YkOrderCallbackReq req) {
        log.info("[YouKuCallbackService] saveOrderCallback req={}", JsonUtil.writeValueAsString(req));
        MiniAppOpenYoukuOrderPo ykOrderCallback = YkCallbackBizMapper.MAPPER.toYkOrderCallback(req);
        // 查询是否存在
        MiniAppOpenYoukuOrderPo orderPo = getOrderById(ykOrderCallback.getOrderId());
        if (orderPo != null) {
            // 存在则更新
            ykOrderCallback.setId(orderPo.getId());
            youkuOrderDao.updateByPrimaryKeySelective(ykOrderCallback);
        } else {
            // 不存在则插入
            youkuOrderDao.insertSelective(ykOrderCallback);
        }
    }

    private MiniAppOpenYoukuOrderPo getOrderById(Long orderId) {
        MiniAppOpenYoukuOrderPoExample example = new MiniAppOpenYoukuOrderPoExample();
        example.createCriteria().andOrderIdEqualTo(orderId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenYoukuOrderPo> pos = youkuOrderDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return null;
        }
        return pos.get(0);
    }
}
