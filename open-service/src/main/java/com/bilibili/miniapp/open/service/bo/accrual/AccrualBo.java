package com.bilibili.miniapp.open.service.bo.accrual;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 预提单BO
 *
 * <AUTHOR>
 * @date 2025/6/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccrualBo {

    /**
     * 自增id
     */
    private Long id;

    /**
     * 小程序id
     */
    private String appId;

    /**
     * 收入日期 20250101，对应明细表日期
     */
    private String incomeDate;

    /**
     * 汇联易预提单id
     */
    private String accrualId;

    /**
     * 提现状态，由结算单的结算状态决定：0-提现冻结，1-可提现，2-提现中，3-提现成功
     */
    private Integer withdrawStatus;

    /**
     * 总金额，单位（分）
     */
    private Long totalAmount;
}
