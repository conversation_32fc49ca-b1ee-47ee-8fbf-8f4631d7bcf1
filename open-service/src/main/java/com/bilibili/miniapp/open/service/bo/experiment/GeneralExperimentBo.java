package com.bilibili.miniapp.open.service.bo.experiment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10
 * @description 通用实验bo
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GeneralExperimentBo {

    private List<Long> hitTunnelIds;

    public static GeneralExperimentBo getDefault() {
        return GeneralExperimentBo.builder()
                .hitTunnelIds(Collections.emptyList())
                .build();
    }
}
