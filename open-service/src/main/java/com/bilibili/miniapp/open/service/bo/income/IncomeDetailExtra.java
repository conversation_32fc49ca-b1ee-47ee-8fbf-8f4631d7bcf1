package com.bilibili.miniapp.open.service.bo.income;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/6/5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IncomeDetailExtra {

    /**
     * 通道费比例
     */
    private String channelFeeRatio;

    /**
     * 分成比例
     */
    private String distributableRatio;
}
