package com.bilibili.miniapp.open.service.bo.settlement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发起提现单请求BO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementOrderInitiateBo {

    /**
     * 结算单ID
     */
    private String settlementId;

    /**
     * 发票OID
     */
    private String invoiceOid;

    /**
     * 发票URL
     */
    private String invoiceUrl;
}
