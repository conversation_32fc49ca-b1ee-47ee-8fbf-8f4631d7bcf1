package com.bilibili.miniapp.open.service.config;

import com.bilibili.bcg.abyss.sdk.AbyssClient;
import com.bilibili.bcg.abyss.sdk.AbyssPlot;
import com.bilibili.bcg.nebula.sdk.NebulaClient;
import lombok.Data;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/6/10
 * @description 商业实验平台配置类
 */
@Component
public class AbyssExpSample {
    /**
     * 需要配置的业务线id
     */
    @Value("${abyss.biz.plotId:280}")
    private int plotId;

    // 声明abyssClient
    @Bean("abyssClient")
    public AbyssClient abyssClient(NebulaClient nebulaClient) throws SchedulerException {
        return AbyssClient.builder()
                .nebulaClient(nebulaClient)
                .build();
    }

    // 生成abyssPlot
    @Bean("abyssPlot")
    public AbyssPlot abyssPlot(AbyssClient abyssClient) throws SchedulerException {
        return abyssClient
                .newPlotBuilder()
                .plotId(plotId)
                .build();
    }

    @Data
    public static class AbyssSampleRequest {
        private long mid;
        private String buvid;
        private String sid;
    }

}
