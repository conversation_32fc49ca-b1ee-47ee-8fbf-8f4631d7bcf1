package com.bilibili.miniapp.open.service.databus.consumer;

import com.alibaba.fastjson.JSON;
import com.bilibili.business.cmpt.idatabus.client.spring.ConsumeMessageContext;
import com.bilibili.business.cmpt.idatabus.client.spring.annotion.DataBusConsumer;
import com.bilibili.miniapp.open.common.enums.ContractStatusEnum;
import com.bilibili.miniapp.open.service.biz.contract.IContractService;
import com.bilibili.miniapp.open.service.databus.entity.ContractMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/30
 */
@Service
@Slf4j
@DataBusConsumer("ContractCenterConsumer")
public class ContractCenterConsumer extends AbstractConsumer<ContractMsg> {

    @Autowired
    private IContractService contractService;

    @Override
    protected void doConsume(ContractMsg contractMsg, ConsumeMessageContext ctx) throws Exception {

        ContractStatusEnum status = ContractStatusEnum.getByContractCenterStatus(contractMsg.getState(), contractMsg.getStage());
        if (status == null) {
            log.warn("接收到的合同变更消息转换的状态忽略:{}", JSON.toJSONString(contractMsg));
            return;
        }

        contractService.updateContractStatusFromDataBus(contractMsg.getContractId(), status, contractMsg.getTimestamp() * 1000);
    }

    @Override
    protected boolean ackIfNecessary() {
        return true;
    }
}
