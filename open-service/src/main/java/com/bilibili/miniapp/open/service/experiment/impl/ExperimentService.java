package com.bilibili.miniapp.open.service.experiment.impl;

import com.bilibili.bcg.abyss.sdk.AbyssPlot;
import com.bilibili.bcg.abyss.sdk.AbyssRichPath;
import com.bilibili.miniapp.open.service.bo.experiment.GeneralExperimentBo;
import com.bilibili.miniapp.open.service.config.AbyssExpSample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.bilibili.miniapp.open.service.experiment.IExperimentService;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/10
 * @description 实验service
 */
@Service
@Slf4j
public class ExperimentService implements IExperimentService {

    public static final String ID = "ExperimentService";
    @Resource(name = "abyssPlot")
    private AbyssPlot abyssPlot;

    @Override
    public GeneralExperimentBo getGeneralParam(Long mid) {
        try {
            AbyssExpSample.AbyssSampleRequest abyssSampleReq = new AbyssExpSample.AbyssSampleRequest();
            abyssSampleReq.setMid(mid);

            AbyssPlot.Request abyssPlotReq = AbyssPlot.Request.builder().build();
            AbyssRichPath abyssRichPath = abyssPlot.selectPathByPojo(abyssPlotReq, abyssSampleReq);
            List<Long> hitTunnelIds = abyssRichPath.getLayerHits().stream()
                    .map(AbyssRichPath.LayerHit::getTunnelId)
                    .collect(Collectors.toList());
            return GeneralExperimentBo.builder()
                    .hitTunnelIds(hitTunnelIds)
                    .build();
        } catch (Exception e) {
            log.warn(ID + "用户:{},获取实验结果失败", mid, e);
            return GeneralExperimentBo.getDefault();
        }
    }
}
