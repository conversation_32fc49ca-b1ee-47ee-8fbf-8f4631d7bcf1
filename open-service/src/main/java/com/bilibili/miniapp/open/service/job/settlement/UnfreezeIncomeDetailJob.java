package com.bilibili.miniapp.open.service.job.settlement;

import com.bilibili.miniapp.open.service.biz.income.IIncomeService;
import com.bilibili.miniapp.open.service.job.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2025/6/12
 */
@Component
@JobHandler("UnfreezeIncomeDetailJob")
@Slf4j
public class UnfreezeIncomeDetailJob extends AbstractJobHandler {


    @Autowired
    private IIncomeService incomeService;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Override
    public ReturnT<String> doExecute(String yyyyMMdd) throws Exception {
        if (StringUtils.isBlank(yyyyMMdd)) {
            yyyyMMdd = LocalDate.now().format(DATE_TIME_FORMATTER);
        }

        incomeService.unfreeze(yyyyMMdd);
        return ReturnT.SUCCESS;
    }
}
