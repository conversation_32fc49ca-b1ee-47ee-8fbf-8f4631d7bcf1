package com.bilibili.miniapp.open.service.mapper;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPo;
import com.bilibili.miniapp.open.service.bo.accrual.AccrualBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 预提单映射器
 *
 * <AUTHOR>
 * @date 2025/6/6
 */
@Mapper
public interface AccrualMapper {

    AccrualMapper MAPPER = Mappers.getMapper(AccrualMapper.class);

    AccrualBo poToBo(MiniAppOpenAccrualPo po);

    List<AccrualBo> poListToBoList(List<MiniAppOpenAccrualPo> poList);
}
