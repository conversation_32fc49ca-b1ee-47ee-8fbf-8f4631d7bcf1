package com.bilibili.miniapp.open.service.mapper;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettleRulePo;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaSettleRuleBo;
import com.bilibili.miniapp.open.service.biz.settlement.model.SettleRule;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/29
 **/

@Mapper
public interface SettlementMapper {

    SettlementMapper MAPPER = Mappers.getMapper(SettlementMapper.class);

    IaaSettleRuleBo toBo(IaaSettleRulePo po);

    @Mapping(target = "businessCashPercent", source = "BPct")
    @Mapping(target = "businessCrmChargePercent", source = "BCrmPct")
    @Mapping(target = "naturalCashPercentBelowThreshold", source = "NBelowPct")
    @Mapping(target = "naturalCashPercentAboveThreshold", source = "NAbovePct")
    @Mapping(target = "monthIncomeThreshold", source = "NThreshold")
    SettleRule toSettleRule(IaaSettleRuleBo bo);

    @Mapping(target = "period", source = "period")
    @Mapping(target = "bPct", source = "BPct")
    @Mapping(target = "bCrmPct", source = "BCrmPct")
    @Mapping(target = "nBelowPct", source = "NBelowPct")
    @Mapping(target = "nAbovePct", source = "NAbovePct")
    @Mapping(target = "nThreshold", source = "NThreshold")
    IaaSettleRulePo toPo(IaaSettleRuleBo bo);
}
