package com.bilibili.miniapp.open.service.rpc.http.dto;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import lombok.Data;
import org.apache.catalina.LifecycleState;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
@Data
@JSONType(naming = PropertyNamingStrategy.SnakeCase)
public class ContractDetailListDto {
    private List<ContractDetailDto> list;
}
